/**
 * Custom Fields Webhook Handler
 *
 * Handles incoming webhook events from Custom Fields platform.
 * Processes contact creation and update events with calendar filtering,
 * proper validation, error handling, and database logging.
 */

import type { Context } from "hono";
import apiClient from "@apiClient";

/**
 * Handle Custom Fields webhook events
 *
 * @param c - Hono context object
 * @returns JSON response with processing results
 */
export async function cfHandler(c: Context): Promise<Response> {

    const apCustomFields = await apiClient.ap.apCustomfield.all()
    const ccCustomFields = await apiClient.cc.ccCustomfieldReq.all()

    return c.json(
        {
            timestamp: new Date().toISOString(),
            apCustomFields,
            ccCustomFields,
        },
        200,
    );
}   
