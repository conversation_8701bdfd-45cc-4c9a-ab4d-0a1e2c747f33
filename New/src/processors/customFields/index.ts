/**
 * Custom Field Conversion Utilities
 *
 * Bidirectional custom field conversion utilities for AutoPatient and CliniCore platforms.
 * Provides intelligent field type mapping, reversible transformations, and comprehensive
 * logging for data synchronization operations.
 *
 * **Available Functions:**
 * - `apToCcCustomFieldConvert`: Convert AutoPatient fields to CliniCore format
 * - `ccToApCustomFieldConvert`: Convert CliniCore fields to AutoPatient format
 *
 * **Key Features:**
 * - Bidirectional field type conversion with data integrity preservation
 * - Intelligent boolean detection (Yes/No variants → boolean fields)
 * - Comprehensive option mapping for select/radio fields
 * - Graceful fallback handling for unmappable field types
 * - Detailed logging with request ID tracing
 * - Strict TypeScript compliance without `any` usage
 * - Performance-optimized for bulk conversion operations
 *
 * @example
 * ```typescript
 * import { apToCcCustomFieldConvert, ccToApCustomFieldConvert } from '@processors/customFields';
 *
 * // Convert AP field to CC format
 * const ccField = apToCcCustomFieldConvert(apField, requestId);
 *
 * // Convert CC field to AP format  
 * const apField = ccToApCustomFieldConvert(ccField, requestId);
 *
 * // Bidirectional conversion should preserve data integrity
 * const originalAp = apField;
 * const convertedCc = apToCcCustomFieldConvert(originalAp, requestId);
 * const backToAp = ccToApCustomFieldConvert(convertedCc, requestId);
 * // backToAp should maintain compatibility with originalAp
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

export { default as apToCcCustomFieldConvert } from "./apToCcCustomFieldConvert";
export { default as ccToApCustomFieldConvert } from "./ccToApCustomFieldConvert";
