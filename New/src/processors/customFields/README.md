# Custom Field Conversion Utilities

Bidirectional custom field conversion utilities for AutoPatient (AP) and CliniCore (CC) platforms with intelligent field type mapping, reversible transformations, and comprehensive logging.

## Overview

This module provides two main functions for converting custom field definitions between AutoPatient and CliniCore systems:

- **`apToCcCustomFieldConvert`**: Converts AutoPatient custom fields to CliniCore format
- **`ccToApCustomFieldConvert`**: Converts CliniCore custom fields to AutoPatient format

## Key Features

- ✅ **Bidirectional Conversion**: Reversible transformations preserve data integrity
- ✅ **Intelligent Type Mapping**: Smart detection of boolean fields (Yes/No variants)
- ✅ **Comprehensive Logging**: Detailed logging with request ID tracing
- ✅ **Graceful Fallbacks**: Unmappable types convert to text fields with warnings
- ✅ **Strict TypeScript**: No `any` usage, full type safety
- ✅ **Performance Optimized**: Pure functions suitable for bulk operations

## Field Type Conversion Matrix

### AutoPatient → CliniCore

| AP Field Type | AP Options | CC Field Type | CC Properties |
|---------------|------------|---------------|---------------|
| `RADIO` | 2 Yes/No variants | `boolean` | - |
| `RADIO` | >2 options or non-Yes/No | `select` | `allowMultipleValues: false` |
| `MULTIPLE_OPTIONS` | Any options | `select` | `allowMultipleValues: true` |
| `SINGLE_OPTIONS` | Any options | `select` | `allowMultipleValues: false` |
| `TEXT` | - | `text` | - |
| *Other types* | - | `text` | *Fallback conversion* |

### CliniCore → AutoPatient

| CC Field Type | CC Properties | AP Field Type | AP Properties |
|---------------|---------------|---------------|---------------|
| `boolean` | - | `RADIO` | `options: ["Yes", "No"]` |
| `select-or-custom` | Any values | `SINGLE_OPTIONS` | Preserves allowed values |
| `select` | `allowMultipleValues: true` | `MULTIPLE_OPTIONS` | Preserves allowed values |
| `select` | `allowMultipleValues: false` | `RADIO` | Preserves allowed values |
| `text`, `textarea`, `email`, `telephone` | - | `TEXT` | - |
| *Other types* | - | `TEXT` | *Fallback conversion* |

## Usage Examples

### Basic Conversion

```typescript
import { apToCcCustomFieldConvert, ccToApCustomFieldConvert } from '@processors/customFields';

// Convert AP boolean field to CC
const apBooleanField: APGetCustomFieldType = {
  id: "field123",
  name: "Newsletter Subscription", 
  dataType: "RADIO",
  picklistOptions: ["Yes", "No"]
};

const ccField = apToCcCustomFieldConvert(apBooleanField, "req-123");
// Result: { name: "Newsletter Subscription", type: "boolean", ... }

// Convert CC select field to AP
const ccSelectField: GetCCCustomField = {
  id: 1,
  name: "interests",
  label: "User Interests",
  type: "select",
  allowMultipleValues: true,
  allowedValues: [
    { id: 1, value: "Sports" },
    { id: 2, value: "Music" }
  ]
};

const apField = ccToApCustomFieldConvert(ccSelectField, "req-123");
// Result: { name: "User Interests", dataType: "MULTIPLE_OPTIONS", options: ["Sports", "Music"] }
```

### Bidirectional Conversion

```typescript
// Test reversibility
const originalAp: APGetCustomFieldType = {
  id: "test123",
  name: "Test Field",
  dataType: "RADIO", 
  picklistOptions: ["Yes", "No"]
};

// AP → CC → AP conversion
const convertedCc = apToCcCustomFieldConvert(originalAp, "req-123");
const backToAp = ccToApCustomFieldConvert(convertedCc, "req-123");

// backToAp should maintain compatibility with originalAp
console.log(backToAp.dataType); // "RADIO"
console.log(backToAp.options); // ["Yes", "No"]
```

### Error Handling

```typescript
// Unmappable field types are handled gracefully
const unknownApField: APGetCustomFieldType = {
  id: "unknown123",
  name: "Unknown Field",
  dataType: "CUSTOM_TYPE" // Not a standard type
};

const fallbackCc = apToCcCustomFieldConvert(unknownApField, "req-123");
// Result: { type: "text", ... } with warning logged
```

## Logging

All conversions include comprehensive logging:

- **DEBUG Level**: Detailed conversion steps, field mappings, option transformations
- **WARN Level**: Fallback conversions, unmappable field types, potential data loss

Example log output:
```
[req-123] DEBUG Custom Field AP→CC conversion started: Newsletter Subscription { apFieldType: "RADIO", hasOptions: true, optionCount: 2 }
[req-123] DEBUG Custom Field AP→CC RADIO→boolean conversion: Newsletter Subscription { detectedAsBoolean: true, originalOptions: ["Yes", "No"] }
```

## Yes/No Detection

The system intelligently detects boolean fields by recognizing Yes/No variants in multiple languages:

- **Yes variants**: "yes", "ja", "true", "1", "y", "oui", "si"
- **No variants**: "no", "nein", "false", "0", "n", "non"

Fields with exactly 2 options matching one yes and one no variant are converted to boolean type.

## Performance Considerations

- **Pure Functions**: No side effects, safe for concurrent use
- **Minimal Memory**: Efficient object creation and array operations
- **Early Returns**: Optimized execution paths for common cases
- **Bulk Operations**: Suitable for processing large numbers of fields

## Type Safety

All functions use strict TypeScript typing:
- No `any` or `as any` usage
- Full type inference and checking
- Database schema inferred types where applicable
- Comprehensive JSDoc documentation
