/**
 * AutoPatient to CliniCore Custom Field Conversion Utility
 *
 * Transforms AutoPatient custom field structures to CliniCore format with
 * bidirectional compatibility, comprehensive logging, and graceful error handling.
 *
 * **Key Conversion Rules:**
 * - AP RADIO (2 Yes/No options) → CC boolean
 * - AP RADIO (>2 options or non-Yes/No) → CC select
 * - AP MULTIPLE_OPTIONS → CC select (allowMultipleValues: true)
 * - AP SINGLE_OPTIONS → CC select (allowMultipleValues: false)
 * - AP TEXT → CC text
 * - Fallback: unmappable types → CC text
 *
 * **Features:**
 * - Strict TypeScript compliance (no `any` usage)
 * - Reversible transformations for data integrity
 * - Comprehensive error handling with request ID tracing
 * - Performance-optimized for bulk conversions
 * - Detailed logging for debugging and monitoring
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type { APGetCustomFieldType, PostCCCustomField } from "@type";
import { logCustomField, logWarn } from "@/utils/logger";

/**
 * Convert AutoPatient custom field to CliniCore format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 *
 * @param apField - AutoPatient custom field object to convert
 * @param requestId - Request ID for logging and error tracing
 * @returns CliniCore custom field format ready for API submission
 *
 * @example
 * ```typescript
 * // Convert AP radio field with Yes/No options to CC boolean
 * const apRadioField: APGetCustomFieldType = {
 *   id: "field123",
 *   name: "Newsletter Subscription",
 *   dataType: "RADIO",
 *   picklistOptions: ["Yes", "No"]
 * };
 * 
 * const ccField = apToCcCustomFieldConvert(apRadioField, "req-123");
 * // Result: { name: "Newsletter Subscription", label: "Newsletter Subscription", type: "boolean", ... }
 *
 * // Convert AP multiple options to CC select
 * const apMultiField: APGetCustomFieldType = {
 *   id: "field456", 
 *   name: "Interests",
 *   dataType: "MULTIPLE_OPTIONS",
 *   picklistOptions: ["Sports", "Music", "Travel"]
 * };
 * 
 * const ccMultiField = apToCcCustomFieldConvert(apMultiField, "req-123");
 * // Result: { type: "select", allowMultipleValues: true, allowedValues: [...], ... }
 * ```
 */
export function apToCcCustomFieldConvert(
	apField: APGetCustomFieldType,
	requestId: string,
): PostCCCustomField {
	logCustomField(requestId, "AP→CC conversion started", apField.name, {
		apFieldType: apField.dataType,
		hasOptions: Boolean(apField.picklistOptions?.length),
		optionCount: apField.picklistOptions?.length || 0,
	});

	// Base field structure with common properties
	const baseField: PostCCCustomField = {
		name: apField.name,
		label: apField.name, // Use name as label by default
		type: "text", // Default fallback type
		validation: "{}",
		isRequired: false,
		allowMultipleValues: false,
	};

	// Handle AP RADIO field type
	if (apField.dataType === "RADIO") {
		return handleApRadioField(apField, baseField, requestId);
	}

	// Handle AP MULTIPLE_OPTIONS field type
	if (apField.dataType === "MULTIPLE_OPTIONS") {
		return handleApMultipleOptionsField(apField, baseField, requestId);
	}

	// Handle AP SINGLE_OPTIONS field type
	if (apField.dataType === "SINGLE_OPTIONS") {
		return handleApSingleOptionsField(apField, baseField, requestId);
	}

	// Handle AP TEXT field type
	if (apField.dataType === "TEXT") {
		logCustomField(requestId, "AP→CC TEXT conversion", apField.name);
		return {
			...baseField,
			type: "text",
		};
	}

	// Fallback for unmappable field types
	logWarn(requestId, `AP→CC fallback conversion for unmappable type: ${apField.dataType}`, {
		fieldName: apField.name,
		originalType: apField.dataType,
		convertedType: "text",
	});

	return {
		...baseField,
		type: "text",
	};
}

/**
 * Handle AP RADIO field conversion with intelligent Yes/No detection
 *
 * Converts AP RADIO fields to either CC boolean (for Yes/No variants) or
 * CC select (for other option sets). Provides reversible transformations.
 *
 * @param apField - AP field with RADIO dataType
 * @param baseField - Base CC field structure
 * @param requestId - Request ID for logging
 * @returns Converted CC field (boolean or select type)
 */
function handleApRadioField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
	requestId: string,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	// Check if this is a Yes/No boolean field
	if (isYesNoVariant(options)) {
		logCustomField(requestId, "AP→CC RADIO→boolean conversion", apField.name, {
			detectedAsBoolean: true,
			originalOptions: options,
		});

		return {
			...baseField,
			type: "boolean",
		};
	}

	// Convert to select field for non-boolean radio options
	logCustomField(requestId, "AP→CC RADIO→select conversion", apField.name, {
		optionCount: options.length,
		options: options,
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP MULTIPLE_OPTIONS field conversion
 *
 * Converts AP MULTIPLE_OPTIONS to CC select with allowMultipleValues: true.
 * Preserves all option values for reversible transformation.
 *
 * @param apField - AP field with MULTIPLE_OPTIONS dataType
 * @param baseField - Base CC field structure
 * @param requestId - Request ID for logging
 * @returns CC select field with multiple values enabled
 */
function handleApMultipleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
	requestId: string,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField(requestId, "AP→CC MULTIPLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: true,
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: true,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Handle AP SINGLE_OPTIONS field conversion
 *
 * Converts AP SINGLE_OPTIONS to CC select with allowMultipleValues: false.
 * Maintains single-selection behavior in the target system.
 *
 * @param apField - AP field with SINGLE_OPTIONS dataType
 * @param baseField - Base CC field structure
 * @param requestId - Request ID for logging
 * @returns CC select field with single value selection
 */
function handleApSingleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
	requestId: string,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField(requestId, "AP→CC SINGLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: false,
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues: options.map((option: string) => ({ value: option })),
	};
}

/**
 * Check if options array represents Yes/No boolean variants
 *
 * Detects various Yes/No representations for intelligent boolean conversion.
 * Supports multiple languages and formats for international compatibility.
 *
 * @param options - Array of option strings to analyze
 * @returns True if options represent a Yes/No boolean choice
 *
 * @example
 * ```typescript
 * isYesNoVariant(["Yes", "No"]) // true
 * isYesNoVariant(["yes", "no"]) // true  
 * isYesNoVariant(["Ja", "Nein"]) // true
 * isYesNoVariant(["true", "false"]) // true
 * isYesNoVariant(["Option A", "Option B"]) // false
 * isYesNoVariant(["Yes", "No", "Maybe"]) // false
 * ```
 */
function isYesNoVariant(options: string[]): boolean {
	// Must have exactly 2 options
	if (options.length !== 2) {
		return false;
	}

	// Normalize options to lowercase for comparison
	const normalizedOptions = options.map((opt) => opt.toLowerCase().trim());

	// Define Yes/No variants (supports multiple languages)
	const yesVariants = ["yes", "ja", "true", "1", "y", "oui", "si"];
	const noVariants = ["no", "nein", "false", "0", "n", "non"];

	// Check if we have one yes variant and one no variant
	const hasYes = normalizedOptions.some((opt) => yesVariants.includes(opt));
	const hasNo = normalizedOptions.some((opt) => noVariants.includes(opt));

	return hasYes && hasNo;
}

export default apToCcCustomFieldConvert;
